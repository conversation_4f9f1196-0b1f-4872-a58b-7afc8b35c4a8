#!/usr/bin/env python3
"""
Test script to verify the processed file session functionality.
This script tests the new processed_file session storage and get_file_path function.
"""

import os
import sys
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.json_session_service import set_session_value, get_session_value
from app.services.nlp_service import get_file_path
from app.services.session_service import get_session_status, get_session_by_id

def test_processed_file_functionality():
    """Test the processed file session functionality"""
    
    # Use an existing session ID for testing
    test_session_id = "706f84f1c9"
    
    print(f"Testing processed file functionality with session: {test_session_id}")
    print("=" * 60)
    
    # 1. Test getting current session status (before adding processed file)
    print("1. Current session status:")
    status = get_session_status()
    print(f"   Session active: {status.get('active', False)}")
    print(f"   Has uploaded file: {status.get('has_file', False)}")
    print(f"   Has processed file: {status.get('has_processed_file', False)}")
    print()
    
    # 2. Test getting session by ID (before adding processed file)
    print("2. Session data by ID:")
    session_data = get_session_by_id(test_session_id)
    if session_data:
        print(f"   Uploaded file: {session_data.get('uploaded_file')}")
        print(f"   Processed file: {session_data.get('processed_file')}")
        print(f"   Has uploaded file: {session_data.get('has_file', False)}")
        print(f"   Has processed file: {session_data.get('has_processed_file', False)}")
    else:
        print("   Session not found!")
        return
    print()
    
    # 3. Test setting a processed file
    print("3. Setting processed file in session:")
    test_processed_filename = "uploadable_text_data_description_okt__정제_ver1.xlsx"
    success = set_session_value(test_session_id, "processed_file", test_processed_filename)
    print(f"   Set processed file: {success}")
    print(f"   Filename: {test_processed_filename}")
    print()
    
    # 4. Test getting the processed file
    print("4. Getting processed file from session:")
    retrieved_filename = get_session_value(test_session_id, "processed_file")
    print(f"   Retrieved filename: {retrieved_filename}")
    print()
    
    # 5. Test get_file_path function with different file types
    print("5. Testing get_file_path function:")
    
    # Test uploaded file path
    uploaded_filename = get_session_value(test_session_id, "uploaded_file")
    if uploaded_filename:
        uploaded_path = get_file_path(test_session_id, uploaded_filename, "uploaded")
        print(f"   Uploaded file path: {uploaded_path}")
        print(f"   Uploaded file exists: {os.path.exists(uploaded_path)}")
    
    # Test processed file path
    if retrieved_filename:
        processed_path = get_file_path(test_session_id, retrieved_filename, "processed")
        print(f"   Processed file path: {processed_path}")
        print(f"   Processed file exists: {os.path.exists(processed_path)}")
    print()
    
    # 6. Test updated session status (after adding processed file)
    print("6. Updated session data by ID:")
    updated_session_data = get_session_by_id(test_session_id)
    if updated_session_data:
        print(f"   Uploaded file: {updated_session_data.get('uploaded_file')}")
        print(f"   Processed file: {updated_session_data.get('processed_file')}")
        print(f"   Has uploaded file: {updated_session_data.get('has_file', False)}")
        print(f"   Has processed file: {updated_session_data.get('has_processed_file', False)}")
    print()
    
    # 7. Test backward compatibility (default file_type)
    print("7. Testing backward compatibility:")
    if uploaded_filename:
        default_path = get_file_path(test_session_id, uploaded_filename)  # No file_type specified
        explicit_path = get_file_path(test_session_id, uploaded_filename, "uploaded")
        print(f"   Default path: {default_path}")
        print(f"   Explicit path: {explicit_path}")
        print(f"   Paths match: {default_path == explicit_path}")
    print()
    
    print("Test completed!")
    print("=" * 60)

if __name__ == "__main__":
    test_processed_file_functionality()
