# current_app/blueprints/analysis/tfidf_routes.py
import json
import logging
import os
import random
import traceback
import uuid

from ...services.file_processor import get_file_columns
from flask import Blueprint, current_app, jsonify, request, send_file, url_for
from werkzeug.utils import secure_filename

from ...services.json_session_service import (get_session_value,
                                              set_session_value)
from ...services.nlp_service import (NGramAnalyzer,
                                     WordCloudGenerator, get_file_path)
from ...services.session_service import get_or_create_session_id
from . import ngram_bp


# Configure CORS for React frontend with HTTPS support
@ngram_bp.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

    # If specific origins are provided, use them
    if cors_allowed_origins != "*":
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get("Origin")
        if request_origin:
            if "," in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(",")
                if request_origin in allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
    else:
        # Wildcard for development
        response.headers.add("Access-Control-Allow-Origin", "*")

    response.headers.add(
        "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
    )
    response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
    response.headers.add("Access-Control-Allow-Credentials", "true")

    # Add Vary header to ensure proper caching with CORS
    response.headers.add("Vary", "Origin")

    # Set UTF-8 charset for JSON responses to properly handle Korean characters
    if response.content_type and response.content_type.startswith('application/json'):
        response.content_type = 'application/json; charset=utf-8'

    return response


@ngram_bp.route("/analyze", methods=["POST"])
def analyze():
    """파일 분석 API"""
    # This analyze the words and generate a wordcloud from all words
    session_id = get_or_create_session_id()
    # except Exception as e:
    #     return jsonify({"error": f"Session ID is required {str(e)}"), 500

    try:
        # 필수 파라미터 확인
        column_name = request.form.get("column_name")
        if not column_name:
            return jsonify({"error": "분석할 컬럼을 선택해주세요"}), 400

        # n-gram 설정 가져오기
        n_gram = request.form.get("n_gram", "2")  # 기본값 2
        max_features = request.form.get("max_features", "3000")

        # 워드클라우드 옵션 (있는 경우에만)
        selection_type = request.form.get("selection_type", "manual")
        max_words = request.form.get("max_words")
        cloud_shape = request.form.get("cloud_shape", "rectangle")
        cloud_color = request.form.get("cloud_color", "viridis")
        selected_words = request.form.get("selected_words")

        # 파일 저장
        filename = get_session_value(session_id, "uploaded_file")
        file_path = get_file_path(session_id, filename, "uploaded")

        # 분석 실행
        analyzer = NGramAnalyzer()
        result = analyzer.analyze(
            file_path=file_path,
            column_name=column_name,
            n_gram=n_gram,
            max_features=max_features,
            selection_type=selection_type,
            max_words=max_words,
            cloud_shape=cloud_shape,
            cloud_color=cloud_color,
            selected_words=selected_words,
        )

        if "error" in result:
            return jsonify(result), 400

        # Add download URLs for all result files
        if 'output_file' in result:
            result["excel_download_url"] = url_for(
                "process.serve_file",
                filepath=result["output_file"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        if 'file_path' in result:
            result["wordcloud_download_url"] = url_for(
                "process.serve_file",
                filepath=result["file_path"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        return jsonify(result)
    except Exception as e:
        current_app.logger.error(f"분석 처리 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


@ngram_bp.route("/get_word_data", methods=["POST"])
def get_word_data():
    """단어 목록 데이터만 반환하는 API (수동 선택 모드용)"""
    # This analyze the words return a few (300) words and their count. The user can then use these words to manually generate a wordcloud
    try:
        session_id = get_or_create_session_id()
        column_name = request.form.get("column_name")
        if not column_name:
            return jsonify({"error": "분석할 컬럼을 선택해주세요"}), 400

        # n-gram 설정 가져오기
        n_gram = request.form.get("n_gram", "2")  # 기본값 2
        max_features = request.form.get("max_features", "3000")

        # 파일 저장
        filename = get_session_value(session_id, "uploaded_file")
        file_path = get_file_path(session_id, filename, "uploaded")

        # 분석 실행 - 수동 선택 모드로 실행하여 단어 데이터만 반환
        analyzer = NGramAnalyzer()
        result = analyzer.analyze(
            file_path=file_path,
            column_name=column_name,
            n_gram=n_gram,
            max_features=max_features,
            selection_type="manual",
        )

        if "error" in result:
            return jsonify(result), 400

        # Add download URLs for all result files
        if 'output_file' in result:
            result["excel_download_url"] = url_for(
                "process.serve_file",
                filepath=result["output_file"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        if 'file_path' in result:
            result["wordcloud_download_url"] = url_for(
                "process.serve_file",
                filepath=result["file_path"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                ** result
            }
        )
    except Exception as e:
        current_app.logger.error(f"단어 데이터 처리 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


@ngram_bp.route("/generate_wordcloud", methods=["POST"])
def generate_wordcloud():
    """선택한 단어로 워드클라우드 생성 API"""
    # This generate a wordcloud from a list of words. The user gets the words by calling the get_word_data endpoint
    session_id = get_or_create_session_id()
    try:
        selected_words = request.form.get("selected_words", "[]")
        cloud_shape = request.form.get("cloud_shape", "rectangle")
        cloud_color = request.form.get("cloud_color", "viridis")
        n_gram = request.form.get("n_gram", "2")  # n-gram 값 추가

        word_list = json.loads(selected_words)
        if not word_list or len(word_list) < 10:
            return jsonify({"error": "최소 10개 이상의 단어를 선택해주세요."}), 400

        # 결과 파일용 고유 파일명 생성
        filename = f"ngram_{n_gram}_wordcloud_{uuid.uuid4().hex[:8]}"
        wordcloud_filename = f"{filename}.png"
        
        count_dict = {word: random.randint(10, 100) for word in word_list}
        wordcloud_generator = WordCloudGenerator(analysis_type="ngram")
        wordcloud_result = wordcloud_generator.generate_wordcloud(
            count_dict=count_dict,
            filename_base=wordcloud_filename,
            cloud_shape=cloud_shape or "rectangle",
            cloud_color=cloud_color or "viridis",
            session_id=session_id,
        )
        # wordcloud_path = wordcloud_result["file_path"]
        if 'file_path' in wordcloud_result:
            wordcloud_result["download_url"] = url_for(
                "process.serve_file",
                filepath=wordcloud_result["file_path"],
                download="true",
                session_id=session_id,
                analysis_category=current_app.config["WORD_CLOUD_FOLDER"],
                _external=True,
            )
        # Add wordcloud result to main result
        if wordcloud_result.get("success"):
            wordcloud_result["wordcloud_file"] = wordcloud_result["wordcloud_file"]
            print(
                f"워드클라우드 생성 성공: {wordcloud_result['wordcloud_file']}"
            )
        
        # 단어-빈도 사전 생성 (다양한 크기로 표현하기 위해 랜덤 빈도 부여)
        

        # 워드클라우드 생성 - 개선된 함수 사용
        

        return jsonify(wordcloud_result)
    except Exception as e:
        # logging.logger.error(f"워드클라우드 생성 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500
