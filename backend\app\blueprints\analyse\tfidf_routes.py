# app/blueprints/analysis/tfidf_routes.py
import json
import logging
import os
import traceback

from flask import Blueprint, jsonify, request, send_file, url_for
from werkzeug.utils import secure_filename

from ...services.json_session_service import (get_session_value,
                                              set_session_value)
from ...services.nlp_service import (TFIDFAnalyzer, analyze_frequency, WordCloudGenerator,
                                     create_topic_network,
                                     create_updated_pyldavis, get_file_path,
                                     get_lda_model, get_updated_topics,
                                     read_file, run_lda_analysis,
                                     run_lda_analysis_with_edits,
                                     update_topic_visualizations)
from ...services.session_service import get_or_create_session_id
from . import tfidf_bp

# Setup logging
logger = logging.getLogger(__name__)


# Configure CORS for React frontend with HTTPS support
@tfidf_bp.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

    # If specific origins are provided, use them
    if cors_allowed_origins != "*":
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get("Origin")
        if request_origin:
            if "," in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(",")
                if request_origin in allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
    else:
        # Wildcard for development
        response.headers.add("Access-Control-Allow-Origin", "*")

    response.headers.add(
        "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
    )
    response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
    response.headers.add("Access-Control-Allow-Credentials", "true")

    # Add Vary header to ensure proper caching with CORS
    response.headers.add("Vary", "Origin")

    # Set UTF-8 charset for JSON responses to properly handle Korean characters
    if response.content_type and response.content_type.startswith('application/json'):
        response.content_type = 'application/json; charset=utf-8'

    return response


@tfidf_bp.route("/get_word_data", methods=["POST", "OPTIONS"])
def get_word_data():
    """Get word list data for manual selection mode"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        session_id = get_or_create_session_id()


        column_name = request.form.get("column_name")
        if not column_name:
            return jsonify({"error": "분석할 컬럼을 선택해주세요"}), 400

        # No processed file info, use uploaded file
        filename = get_session_value(session_id, "uploaded_file")
        if not filename:
            return jsonify({"error": "업로드된 파일이 없습니다"})
        file_path = get_file_path(session_id, filename, "uploaded")

        # Analyze using TFIDFAnalyzer
        analyzer = TFIDFAnalyzer()
        result = analyzer.analyze_tfidf(
            file_path, column_name, session_id, selection_type="manual"
        )

        if "error" in result:
            return jsonify(result), 400

        # Add download URLs for all result files
        if 'file_path' in result:
            result["download_url"] = url_for(
                "process.serve_file",
                filepath=result["file_path"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        if 'output_file' in result:
            result["csv_download_url"] = url_for(
                "process.serve_file",
                filepath=result["output_file"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        return jsonify(
            {
                "success": True,
                **result,
                "session_id": session_id,
            }
        )

    except Exception as e:
        logger.error(f"단어 데이터 처리 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


@tfidf_bp.route("/analyze", methods=["POST", "OPTIONS"])
def analyze():
    """Main TF-IDF analysis endpoint"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        session_id = get_or_create_session_id()

        # File validation
        filename = get_session_value(session_id, "uploaded_file")
        if not filename:
            return jsonify({"error": "파일을 선택해주세요"}), 400

        column_name = request.form.get("column_name")
        if not column_name:
            return jsonify({"error": "분석할 컬럼을 선택해주세요"}), 400

        file_path = get_file_path(session_id, filename, "uploaded")

        # Additional parameters
        selection_type = request.form.get("selection_type", "top_n") # top_n and manual
        max_words = request.form.get("max_words", "50")
        cloud_shape = request.form.get("cloud_shape", "rectangle")
        cloud_color = request.form.get("cloud_color", "viridis")
        selected_words = request.form.get("selected_words", None)

        # Run analysis
        analyzer = TFIDFAnalyzer()
        result = analyzer.analyze_tfidf(
            file_path,
            column_name,
            session_id,
            selection_type,
            max_words,
            cloud_shape,
            cloud_color,
            selected_words,
        )

        if "error" in result:
            return jsonify(result), 400

        # Add download URLs for all result files
        if 'file_path' in result:
            result["download_url"] = url_for(
                "process.serve_file",
                filepath=result["file_path"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        if 'output_file' in result:
            result["csv_download_url"] = url_for(
                "process.serve_file",
                filepath=result["output_file"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        return jsonify({"success": True, "session_id": session_id, **result})

    except Exception as e:
        logger.error(f"분석 처리 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


@tfidf_bp.route("/generate_wordcloud", methods=["POST", "OPTIONS"])
def generate_wordcloud():
    """Generate wordcloud from selected words"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        session_id = get_or_create_session_id()

        selected_words = request.form.get("selected_words", "[]")
        cloud_shape = request.form.get("cloud_shape", "rectangle")
        cloud_color = request.form.get("cloud_color", "viridis")

        word_list = json.loads(selected_words)
        if not word_list or len(word_list) < 10:
            return jsonify({"error": "최소 10개 이상의 단어를 선택해주세요."}), 400

        # Generate wordcloud
        wordcloud_generator = WordCloudGenerator()
        result = wordcloud_generator.generate_from_words(
            word_list, session_id, cloud_shape, cloud_color
        )

        if "error" in result:
            return jsonify(result), 400
        if 'file_path' in result:
            result["download_url"] = url_for(
                "process.serve_file",
                filepath=result["file_path"],
                download="true",
                session_id=session_id,
                _external=True,
            )
        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                ** result
            }
        )

    except Exception as e:
        logger.error(f"워드클라우드 생성 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


@tfidf_bp.route("/edit_words", methods=["POST", "OPTIONS"])
def edit_words():
    """Edit words and regenerate wordcloud"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        session_id = get_or_create_session_id()

        edited_words = request.form.get("edited_words", "[]") # list of dictions [{"new":"new_word", "original":"old_word"}]
        cloud_shape = request.form.get("cloud_shape", "rectangle")
        cloud_color = request.form.get("cloud_color", "viridis")

        word_list = json.loads(edited_words)
        if not word_list or len(word_list) < 10:
            return jsonify({"error": "최소 10개 이상의 단어가 필요합니다."}), 400

        # Generate wordcloud with edited words
        wordcloud_generator = WordCloudGenerator()
        result = wordcloud_generator.generate_from_words(
            word_list, session_id, cloud_shape, cloud_color, prefix="edited"
        )

        if "error" in result:
            return jsonify(result), 400

        if 'file_path' in result:
            result["download_url"] = url_for(
                "process.serve_file",
                filepath=result["file_path"],
                download="true",
                session_id=session_id,
                _external=True,
            )
        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                ** result
            }
        )


    except Exception as e:
        logger.error(f"단어 편집 및 워드클라우드 생성 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500
